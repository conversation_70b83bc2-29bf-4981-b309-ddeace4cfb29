/**
 * Vendor Dashboard Styles
 */

/* Reset and Base Styles */
.vendor-dashboard-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f1f1f1;
    min-height: 100vh;
}

.vendor-dashboard-page {
    margin: 0;
    padding: 0;
}

/* Loading Styles */
.vendor-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    text-align: center;
}

.vendor-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: vendor-spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes vendor-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dashboard Layout */
.vendor-dashboard {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.vendor-header {
    background: #fff;
    border-bottom: 1px solid #ddd;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100%;
    box-sizing: border-box;
}

.vendor-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    min-width: 0;
}

.vendor-sidebar-toggle {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    flex-shrink: 0;
}

.vendor-sidebar-toggle:hover {
    background: #f0f0f0;
}

.vendor-logo {
    flex-shrink: 0;
    min-width: 0;
}

.vendor-logo h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #0073aa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.vendor-header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 0;
}

.vendor-header-right {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 0 0 auto;
    margin-left: auto;
    justify-content: flex-end;
    min-width: fit-content;
}

/* Profile Dropdown */
.vendor-profile-dropdown {
    position: relative;
    flex-shrink: 0;
    margin-left: auto;
}

.vendor-profile-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
    white-space: nowrap;
    min-width: 0;
    justify-content: flex-end;
}

.vendor-profile-button:hover {
    background: #f0f0f0;
}

.vendor-profile-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    min-width: 0;
    text-align: right;
}

.vendor-profile-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.vendor-profile-role {
    font-size: 12px;
    color: #666;
    margin: 0;
    white-space: nowrap;
}

.vendor-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    border-radius: 50%;
    object-fit: cover;
    margin-left: 8px;
}

.vendor-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.vendor-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 180px;
    z-index: 1001;
    padding: 8px 0;
}

.vendor-dropdown-menu a {
    display: block;
    padding: 8px 16px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s;
}

.vendor-dropdown-menu a:hover {
    background: #f0f0f0;
}

.vendor-dropdown-menu hr {
    margin: 8px 0;
    border: none;
    border-top: 1px solid #eee;
}

/* Dashboard Content */
.vendor-dashboard-content {
    display: flex;
    flex: 1;
}

/* Sidebar */
.vendor-sidebar {
    background: #fff;
    border-right: 1px solid #ddd;
    width: 260px;
    transition: width 0.3s ease;
    overflow: hidden;
}

.vendor-sidebar.closed {
    width: 60px;
}

.vendor-nav-list {
    list-style: none;
    margin: 0;
    padding: 20px 0;
}

.vendor-nav-item {
    margin: 0;
}

.vendor-nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    padding: 12px 20px;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
    font-size: 14px;
}

.vendor-nav-link:hover {
    background: #f0f0f0;
    color: #333;
}

.vendor-nav-item.active .vendor-nav-link {
    background: #0073aa;
    color: #fff;
}

.vendor-nav-link .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.vendor-sidebar.closed .vendor-nav-text {
    display: none;
}

/* Main Content */
.vendor-main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.vendor-page {
    max-width: 1200px;
    margin: 0 auto;
}

.vendor-page-header {
    margin-bottom: 30px;
}

.vendor-page-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #333;
}

/* Stats Grid */
.vendor-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.vendor-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.vendor-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.vendor-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vendor-stat-icon .dashicons {
    font-size: 24px;
    color: #fff;
}

.vendor-stat-green .vendor-stat-icon {
    background: #46b450;
}

.vendor-stat-blue .vendor-stat-icon {
    background: #0073aa;
}

.vendor-stat-orange .vendor-stat-icon {
    background: #f56500;
}

.vendor-stat-purple .vendor-stat-icon {
    background: #826eb4;
}

.vendor-stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.vendor-stat-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Dashboard Widgets */
.vendor-dashboard-widgets {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.vendor-widget {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vendor-widget h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Quick Actions */
.vendor-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.vendor-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.2s;
    display: inline-block;
}

.vendor-btn-primary {
    background: #0073aa;
    color: #fff;
}

.vendor-btn-primary:hover {
    background: #005a87;
}

.vendor-btn-secondary {
    background: #f0f0f0;
    color: #333;
}

.vendor-btn-secondary:hover {
    background: #e0e0e0;
}

/* Products Grid */
.vendor-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.vendor-product-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.vendor-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.vendor-product-image {
    height: 200px;
    overflow: hidden;
}

.vendor-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vendor-product-content {
    padding: 16px;
}

.vendor-product-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.vendor-product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.vendor-product-price {
    font-size: 18px;
    font-weight: 700;
    color: #0073aa;
}

.vendor-product-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.vendor-status-publish {
    background: #d4edda;
    color: #155724;
}

.vendor-status-draft {
    background: #fff3cd;
    color: #856404;
}

.vendor-status-private {
    background: #f8d7da;
    color: #721c24;
}

.vendor-product-stock {
    margin-bottom: 12px;
    font-size: 14px;
}

.vendor-stock-status {
    font-weight: 500;
}

.vendor-stock-instock {
    color: #46b450;
}

.vendor-stock-outofstock {
    color: #dc3232;
}

.vendor-product-actions {
    display: flex;
    gap: 8px;
}

.vendor-btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* Tables */
.vendor-table {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-collapse: collapse;
}

.vendor-table th,
.vendor-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.vendor-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.vendor-table tbody tr:hover {
    background: #f8f9fa;
}

/* Filters */
.vendor-filters {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    align-items: center;
}

.vendor-search-input,
.vendor-filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.vendor-search-input {
    min-width: 250px;
}

/* Balance Card */
.vendor-balance-card {
    background: linear-gradient(135deg, #0073aa, #005a87);
    color: #fff;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 30px;
    text-align: center;
}

.vendor-balance-card h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    opacity: 0.9;
}

.vendor-balance-amount {
    font-size: 32px;
    font-weight: 700;
}

/* Modal */
.vendor-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.vendor-modal {
    background: #fff;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.vendor-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
}

.vendor-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.vendor-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vendor-withdrawal-form {
    padding: 24px;
}

.vendor-form-group {
    margin-bottom: 20px;
}

.vendor-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
}

.vendor-form-group input,
.vendor-form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.vendor-form-group small {
    display: block;
    margin-top: 4px;
    color: #666;
    font-size: 12px;
}

.vendor-form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.vendor-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

/* Pagination */
.vendor-pagination {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 30px;
}

.vendor-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s;
}

.vendor-pagination-btn:hover:not(:disabled) {
    background: #f0f0f0;
}

.vendor-pagination-btn.active {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.vendor-pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* No Items */
.vendor-no-items {
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vendor-no-items p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vendor-sidebar {
        position: fixed;
        top: 60px;
        left: 0;
        height: calc(100vh - 60px);
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .vendor-sidebar.open {
        transform: translateX(0);
    }
    
    .vendor-sidebar.closed {
        width: 260px;
        transform: translateX(-100%);
    }
    
    .vendor-main-content {
        padding: 20px;
    }
    
    .vendor-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .vendor-dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .vendor-header {
        padding: 0 15px;
    }

    .vendor-header-left {
        flex: 1;
        min-width: 0;
    }

    .vendor-header-right {
        flex: 0 0 auto;
        margin-left: 10px;
    }

    .vendor-logo h1 {
        font-size: 18px;
    }

    .vendor-name {
        display: none;
    }

    .vendor-profile-button {
        padding: 6px 8px;
        gap: 6px;
    }

    .vendor-avatar {
        width: 28px;
        height: 28px;
    }
}

@media (max-width: 480px) {
    .vendor-main-content {
        padding: 15px;
    }
    
    .vendor-page-header h2 {
        font-size: 24px;
    }
    
    .vendor-stat-card {
        padding: 20px;
    }
    
    .vendor-widget {
        padding: 20px;
    }
}
