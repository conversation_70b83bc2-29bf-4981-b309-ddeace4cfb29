/**
 * Vendor Dashboard React App
 */

const { createElement: e, useState, useEffect, Fragment } = wp.element;
const { apiFetch } = wp;

// Main Dashboard Component
const VendorDashboard = () => {
    const [currentPage, setCurrentPage] = useState('dashboard');
    const [loading, setLoading] = useState(true);
    const [vendor, setVendor] = useState(null);
    const [stats, setStats] = useState(null);
    const [sidebarOpen, setSidebarOpen] = useState(true);

    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            setLoading(true);
            
            const [vendorData, statsData] = await Promise.all([
                apiFetch({ path: '/vendor/v1/vendor/profile' }),
                apiFetch({ path: '/vendor/v1/dashboard/stats' })
            ]);
            
            setVendor(vendorData);
            setStats(statsData);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const menuItems = [
        { id: 'dashboard', label: vendorDashboard.strings.dashboard, icon: 'dashboard' },
        { id: 'products', label: vendorDashboard.strings.products, icon: 'products-alt' },
        { id: 'orders', label: vendorDashboard.strings.orders, icon: 'cart' },
        { id: 'coupons', label: vendorDashboard.strings.coupons, icon: 'tickets-alt' },
        { id: 'analytics', label: vendorDashboard.strings.analytics, icon: 'chart-line' },
        { id: 'reviews', label: vendorDashboard.strings.reviews, icon: 'star-filled' },
        { id: 'withdrawals', label: vendorDashboard.strings.withdrawals, icon: 'money-alt' },
        ...(vendorDashboard.tutorActive ? [
            { id: 'courses', label: vendorDashboard.strings.courses, icon: 'book' },
            { id: 'announcements', label: vendorDashboard.strings.announcements, icon: 'megaphone' },
            { id: 'qa', label: vendorDashboard.strings.qa, icon: 'editor-help' },
            { id: 'quiz-attempts', label: vendorDashboard.strings.quiz_attempts, icon: 'clipboard' },
            { id: 'students', label: vendorDashboard.strings.students, icon: 'groups' }
        ] : []),
        { id: 'settings', label: vendorDashboard.strings.settings, icon: 'admin-settings' }
    ];

    if (loading) {
        return e(LoadingSpinner);
    }

    return e('div', { className: 'vendor-dashboard' },
        e(Header, { 
            vendor, 
            sidebarOpen, 
            setSidebarOpen 
        }),
        e('div', { className: 'vendor-dashboard-content' },
            e(Sidebar, { 
                menuItems, 
                currentPage, 
                setCurrentPage, 
                sidebarOpen 
            }),
            e('main', { className: 'vendor-main-content' },
                e(PageContent, { 
                    currentPage, 
                    vendor, 
                    stats 
                })
            )
        )
    );
};

// Header Component
const Header = ({ vendor, sidebarOpen, setSidebarOpen }) => {
    const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);

    return e('header', { className: 'vendor-header' },
        e('div', { className: 'vendor-header-left' },
            e('button', {
                className: 'vendor-sidebar-toggle',
                onClick: () => setSidebarOpen(!sidebarOpen)
            },
                e('span', { className: 'dashicons dashicons-menu' })
            ),
            e('div', { className: 'vendor-logo' },
                e('h1', null, vendor?.store_name || 'Vendor Dashboard')
            )
        ),
        e('div', { className: 'vendor-header-right' },
            e('div', { className: 'vendor-profile-dropdown' },
                e('button', {
                    className: 'vendor-profile-button',
                    onClick: () => setProfileDropdownOpen(!profileDropdownOpen)
                },
                    e('span', { className: 'vendor-name' }, vendor?.user?.display_name),
                    e('img', {
                        src: vendor?.store_logo || `https://www.gravatar.com/avatar/${vendor?.user?.email}?s=32&d=mp`,
                        alt: vendor?.user?.display_name,
                        className: 'vendor-avatar'
                    }),
                    e('span', { className: 'dashicons dashicons-arrow-down-alt2' })
                ),
                profileDropdownOpen && e('div', { className: 'vendor-dropdown-menu' },
                    e('a', { 
                        href: '#',
                        onClick: (e) => {
                            e.preventDefault();
                            window.open(vendor?.store_url || '#', '_blank');
                        }
                    }, vendorDashboard.strings.store),
                    e('a', { 
                        href: '#',
                        onClick: (e) => {
                            e.preventDefault();
                            setCurrentPage('settings');
                            setProfileDropdownOpen(false);
                        }
                    }, vendorDashboard.strings.store_settings),
                    e('hr'),
                    e('a', { 
                        href: wp.url.addQueryArgs(window.location.href, { action: 'logout' })
                    }, vendorDashboard.strings.logout)
                )
            )
        )
    );
};

// Sidebar Component
const Sidebar = ({ menuItems, currentPage, setCurrentPage, sidebarOpen }) => {
    return e('aside', { 
        className: `vendor-sidebar ${sidebarOpen ? 'open' : 'closed'}` 
    },
        e('nav', { className: 'vendor-nav' },
            e('ul', { className: 'vendor-nav-list' },
                menuItems.map(item =>
                    e('li', { 
                        key: item.id,
                        className: `vendor-nav-item ${currentPage === item.id ? 'active' : ''}`
                    },
                        e('button', {
                            className: 'vendor-nav-link',
                            onClick: () => setCurrentPage(item.id)
                        },
                            e('span', { className: `dashicons dashicons-${item.icon}` }),
                            e('span', { className: 'vendor-nav-text' }, item.label)
                        )
                    )
                )
            )
        )
    );
};

// Page Content Component
const PageContent = ({ currentPage, vendor, stats }) => {
    switch (currentPage) {
        case 'dashboard':
            return e(DashboardPage, { stats });
        case 'products':
            return e(ProductsPage);
        case 'orders':
            return e(OrdersPage);
        case 'coupons':
            return e(CouponsPage);
        case 'analytics':
            return e(AnalyticsPage);
        case 'reviews':
            return e(ReviewsPage);
        case 'withdrawals':
            return e(WithdrawalsPage);
        case 'courses':
            return e(CoursesPage);
        case 'settings':
            return e(SettingsPage, { vendor });
        default:
            return e(DashboardPage, { stats });
    }
};

// Dashboard Page Component
const DashboardPage = ({ stats }) => {
    return e('div', { className: 'vendor-page vendor-dashboard-page' },
        e('div', { className: 'vendor-page-header' },
            e('h2', null, vendorDashboard.strings.dashboard)
        ),
        e('div', { className: 'vendor-stats-grid' },
            e(StatCard, {
                title: 'Total Earnings',
                value: formatCurrency(stats?.earnings?.total_commission || 0),
                icon: 'money-alt',
                color: 'green'
            }),
            e(StatCard, {
                title: 'Available Balance',
                value: formatCurrency(stats?.earnings?.available_balance || 0),
                icon: 'bank',
                color: 'blue'
            }),
            e(StatCard, {
                title: 'Total Orders',
                value: stats?.orders?.total_orders || 0,
                icon: 'cart',
                color: 'orange'
            }),
            e(StatCard, {
                title: 'Total Products',
                value: stats?.products?.total_products || 0,
                icon: 'products',
                color: 'purple'
            })
        ),
        e('div', { className: 'vendor-dashboard-widgets' },
            e('div', { className: 'vendor-widget' },
                e('h3', null, 'Recent Orders'),
                e('p', null, 'Recent orders widget content...')
            ),
            e('div', { className: 'vendor-widget' },
                e('h3', null, 'Quick Actions'),
                e('div', { className: 'vendor-quick-actions' },
                    e('button', { className: 'vendor-btn vendor-btn-primary' }, 'Add Product'),
                    e('button', { className: 'vendor-btn vendor-btn-secondary' }, 'Create Coupon'),
                    e('button', { className: 'vendor-btn vendor-btn-secondary' }, 'Request Withdrawal')
                )
            )
        )
    );
};

// Stat Card Component
const StatCard = ({ title, value, icon, color }) => {
    return e('div', { className: `vendor-stat-card vendor-stat-${color}` },
        e('div', { className: 'vendor-stat-icon' },
            e('span', { className: `dashicons dashicons-${icon}` })
        ),
        e('div', { className: 'vendor-stat-content' },
            e('h3', null, value),
            e('p', null, title)
        )
    );
};

// Products Page Component
const ProductsPage = () => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('any');

    useEffect(() => {
        loadProducts();
    }, [currentPage, searchTerm, statusFilter]);

    const loadProducts = async () => {
        try {
            setLoading(true);
            const response = await apiFetch({
                path: `/vendor/v1/products?page=${currentPage}&search=${searchTerm}&status=${statusFilter}`
            });
            setProducts(response.products);
            setTotalPages(response.pages);
        } catch (error) {
            console.error('Error loading products:', error);
        } finally {
            setLoading(false);
        }
    };

    return e('div', { className: 'vendor-page vendor-products-page' },
        e('div', { className: 'vendor-page-header' },
            e('h2', null, vendorDashboard.strings.products),
            e('button', {
                className: 'vendor-btn vendor-btn-primary',
                onClick: () => window.open('/wp-admin/post-new.php?post_type=product', '_blank')
            }, vendorDashboard.strings.add_new)
        ),

        e('div', { className: 'vendor-filters' },
            e('div', { className: 'vendor-search' },
                e('input', {
                    type: 'text',
                    placeholder: vendorDashboard.strings.search + '...',
                    value: searchTerm,
                    onChange: (e) => setSearchTerm(e.target.value),
                    className: 'vendor-search-input'
                })
            ),
            e('select', {
                value: statusFilter,
                onChange: (e) => setStatusFilter(e.target.value),
                className: 'vendor-filter-select'
            },
                e('option', { value: 'any' }, vendorDashboard.strings.all),
                e('option', { value: 'publish' }, 'Published'),
                e('option', { value: 'draft' }, 'Draft'),
                e('option', { value: 'private' }, 'Private')
            )
        ),

        loading ? e(LoadingSpinner) : e('div', { className: 'vendor-products-grid' },
            products.length > 0 ? products.map(product =>
                e(ProductCard, { key: product.id, product })
            ) : e('div', { className: 'vendor-no-items' },
                e('p', null, 'No products found.')
            )
        ),

        totalPages > 1 && e(Pagination, {
            currentPage,
            totalPages,
            onPageChange: setCurrentPage
        })
    );
};

// Product Card Component
const ProductCard = ({ product }) => {
    return e('div', { className: 'vendor-product-card' },
        e('div', { className: 'vendor-product-image' },
            e('img', {
                src: product.image || '/wp-content/plugins/woocommerce/assets/images/placeholder.png',
                alt: product.name,
                onError: (e) => {
                    e.target.src = '/wp-content/plugins/woocommerce/assets/images/placeholder.png';
                }
            })
        ),
        e('div', { className: 'vendor-product-content' },
            e('h3', { className: 'vendor-product-title' }, product.name),
            e('div', { className: 'vendor-product-meta' },
                e('span', { className: 'vendor-product-price' },
                    formatCurrency(product.price || product.regular_price)
                ),
                e('span', {
                    className: `vendor-product-status vendor-status-${product.status}`
                }, product.status)
            ),
            e('div', { className: 'vendor-product-stock' },
                e('span', {
                    className: `vendor-stock-status vendor-stock-${product.stock_status}`
                }, product.stock_status === 'instock' ? 'In Stock' : 'Out of Stock'),
                product.stock_quantity && e('span', { className: 'vendor-stock-quantity' },
                    ` (${product.stock_quantity})`
                )
            )
        ),
        e('div', { className: 'vendor-product-actions' },
            e('button', {
                className: 'vendor-btn vendor-btn-small',
                onClick: () => window.open(`/wp-admin/post.php?post=${product.id}&action=edit`, '_blank')
            }, vendorDashboard.strings.edit),
            e('button', {
                className: 'vendor-btn vendor-btn-small vendor-btn-secondary',
                onClick: () => window.open(`/?p=${product.id}`, '_blank')
            }, vendorDashboard.strings.view)
        )
    );
};

// Orders Page Component
const OrdersPage = () => {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [statusFilter, setStatusFilter] = useState('any');

    useEffect(() => {
        loadOrders();
    }, [currentPage, statusFilter]);

    const loadOrders = async () => {
        try {
            setLoading(true);
            const response = await apiFetch({
                path: `/vendor/v1/orders?page=${currentPage}&status=${statusFilter}`
            });
            setOrders(response.orders);
            setTotalPages(response.pages);
        } catch (error) {
            console.error('Error loading orders:', error);
        } finally {
            setLoading(false);
        }
    };

    return e('div', { className: 'vendor-page vendor-orders-page' },
        e('div', { className: 'vendor-page-header' },
            e('h2', null, vendorDashboard.strings.orders)
        ),

        e('div', { className: 'vendor-filters' },
            e('select', {
                value: statusFilter,
                onChange: (e) => setStatusFilter(e.target.value),
                className: 'vendor-filter-select'
            },
                e('option', { value: 'any' }, vendorDashboard.strings.all),
                e('option', { value: 'pending' }, 'Pending'),
                e('option', { value: 'processing' }, 'Processing'),
                e('option', { value: 'completed' }, 'Completed'),
                e('option', { value: 'cancelled' }, 'Cancelled')
            )
        ),

        loading ? e(LoadingSpinner) : e('div', { className: 'vendor-orders-table' },
            orders.length > 0 ? e('table', { className: 'vendor-table' },
                e('thead', null,
                    e('tr', null,
                        e('th', null, 'Order'),
                        e('th', null, 'Customer'),
                        e('th', null, 'Status'),
                        e('th', null, 'Total'),
                        e('th', null, 'Date'),
                        e('th', null, 'Actions')
                    )
                ),
                e('tbody', null,
                    orders.map(order =>
                        e('tr', { key: order.id },
                            e('td', null, `#${order.order_number}`),
                            e('td', null, `${order.billing.first_name} ${order.billing.last_name}`),
                            e('td', null,
                                e('span', {
                                    className: `vendor-status vendor-status-${order.status}`
                                }, order.status)
                            ),
                            e('td', null, formatCurrency(order.total)),
                            e('td', null, formatDate(order.date_created)),
                            e('td', null,
                                e('button', {
                                    className: 'vendor-btn vendor-btn-small',
                                    onClick: () => window.open(`/wp-admin/post.php?post=${order.id}&action=edit`, '_blank')
                                }, vendorDashboard.strings.view)
                            )
                        )
                    )
                )
            ) : e('div', { className: 'vendor-no-items' },
                e('p', null, 'No orders found.')
            )
        ),

        totalPages > 1 && e(Pagination, {
            currentPage,
            totalPages,
            onPageChange: setCurrentPage
        })
    );
};

const CouponsPage = () => e('div', { className: 'vendor-page' }, 
    e('h2', null, vendorDashboard.strings.coupons),
    e('p', null, 'Coupons management coming soon...')
);

const AnalyticsPage = () => e('div', { className: 'vendor-page' }, 
    e('h2', null, vendorDashboard.strings.analytics),
    e('p', null, 'Analytics coming soon...')
);

const ReviewsPage = () => e('div', { className: 'vendor-page' }, 
    e('h2', null, vendorDashboard.strings.reviews),
    e('p', null, 'Reviews management coming soon...')
);

// Withdrawals Page Component
const WithdrawalsPage = () => {
    const [withdrawals, setWithdrawals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showRequestForm, setShowRequestForm] = useState(false);
    const [availableBalance, setAvailableBalance] = useState(0);
    const [paymentMethods, setPaymentMethods] = useState({});

    useEffect(() => {
        loadWithdrawals();
        loadPaymentMethods();
        loadDashboardStats();
    }, []);

    const loadWithdrawals = async () => {
        try {
            const response = await apiFetch({
                path: '/vendor/v1/withdrawals'
            });
            setWithdrawals(response.withdrawals);
        } catch (error) {
            console.error('Error loading withdrawals:', error);
        }
    };

    const loadPaymentMethods = async () => {
        try {
            const response = await apiFetch({
                path: '/vendor/v1/withdrawals/methods'
            });
            setPaymentMethods(response.methods);
        } catch (error) {
            console.error('Error loading payment methods:', error);
        }
    };

    const loadDashboardStats = async () => {
        try {
            const response = await apiFetch({
                path: '/vendor/v1/dashboard/stats'
            });
            setAvailableBalance(response.earnings.available_balance);
            setLoading(false);
        } catch (error) {
            console.error('Error loading stats:', error);
            setLoading(false);
        }
    };

    return e('div', { className: 'vendor-page vendor-withdrawals-page' },
        e('div', { className: 'vendor-page-header' },
            e('h2', null, vendorDashboard.strings.withdrawals),
            e('button', {
                className: 'vendor-btn vendor-btn-primary',
                onClick: () => setShowRequestForm(true),
                disabled: availableBalance <= 0
            }, 'Request Withdrawal')
        ),

        e('div', { className: 'vendor-balance-card' },
            e('h3', null, 'Available Balance'),
            e('div', { className: 'vendor-balance-amount' }, formatCurrency(availableBalance))
        ),

        showRequestForm && e(WithdrawalRequestForm, {
            availableBalance,
            paymentMethods,
            onClose: () => setShowRequestForm(false),
            onSuccess: () => {
                setShowRequestForm(false);
                loadWithdrawals();
                loadDashboardStats();
            }
        }),

        loading ? e(LoadingSpinner) : e('div', { className: 'vendor-withdrawals-table' },
            withdrawals.length > 0 ? e('table', { className: 'vendor-table' },
                e('thead', null,
                    e('tr', null,
                        e('th', null, 'Amount'),
                        e('th', null, 'Method'),
                        e('th', null, 'Status'),
                        e('th', null, 'Requested'),
                        e('th', null, 'Processed')
                    )
                ),
                e('tbody', null,
                    withdrawals.map(withdrawal =>
                        e('tr', { key: withdrawal.id },
                            e('td', null, formatCurrency(withdrawal.amount)),
                            e('td', null, withdrawal.payment_method),
                            e('td', null,
                                e('span', {
                                    className: `vendor-status vendor-status-${withdrawal.status}`
                                }, withdrawal.status)
                            ),
                            e('td', null, formatDate(withdrawal.requested_at)),
                            e('td', null, withdrawal.processed_at ? formatDate(withdrawal.processed_at) : '-')
                        )
                    )
                )
            ) : e('div', { className: 'vendor-no-items' },
                e('p', null, 'No withdrawal requests found.')
            )
        )
    );
};

// Withdrawal Request Form Component
const WithdrawalRequestForm = ({ availableBalance, paymentMethods, onClose, onSuccess }) => {
    const [amount, setAmount] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('');
    const [paymentDetails, setPaymentDetails] = useState({});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (parseFloat(amount) > availableBalance) {
            setError('Amount exceeds available balance');
            return;
        }

        try {
            setLoading(true);
            setError('');

            await apiFetch({
                path: '/vendor/v1/withdrawals',
                method: 'POST',
                data: {
                    amount: parseFloat(amount),
                    payment_method: paymentMethod,
                    payment_details: paymentDetails
                }
            });

            onSuccess();
        } catch (error) {
            setError(error.message || 'Failed to create withdrawal request');
        } finally {
            setLoading(false);
        }
    };

    return e('div', { className: 'vendor-modal-overlay' },
        e('div', { className: 'vendor-modal' },
            e('div', { className: 'vendor-modal-header' },
                e('h3', null, 'Request Withdrawal'),
                e('button', {
                    className: 'vendor-modal-close',
                    onClick: onClose
                }, '×')
            ),
            e('form', { onSubmit: handleSubmit, className: 'vendor-withdrawal-form' },
                e('div', { className: 'vendor-form-group' },
                    e('label', null, 'Amount'),
                    e('input', {
                        type: 'number',
                        step: '0.01',
                        max: availableBalance,
                        value: amount,
                        onChange: (e) => setAmount(e.target.value),
                        required: true
                    }),
                    e('small', null, `Available: ${formatCurrency(availableBalance)}`)
                ),
                e('div', { className: 'vendor-form-group' },
                    e('label', null, 'Payment Method'),
                    e('select', {
                        value: paymentMethod,
                        onChange: (e) => setPaymentMethod(e.target.value),
                        required: true
                    },
                        e('option', { value: '' }, 'Select method...'),
                        Object.entries(paymentMethods).map(([key, label]) =>
                            e('option', { key, value: key }, label)
                        )
                    )
                ),
                error && e('div', { className: 'vendor-error' }, error),
                e('div', { className: 'vendor-form-actions' },
                    e('button', {
                        type: 'button',
                        className: 'vendor-btn vendor-btn-secondary',
                        onClick: onClose
                    }, vendorDashboard.strings.cancel),
                    e('button', {
                        type: 'submit',
                        className: 'vendor-btn vendor-btn-primary',
                        disabled: loading
                    }, loading ? 'Processing...' : 'Request Withdrawal')
                )
            )
        )
    );
};

const CoursesPage = () => e('div', { className: 'vendor-page' }, 
    e('h2', null, vendorDashboard.strings.courses),
    e('p', null, 'Courses management coming soon...')
);

const SettingsPage = ({ vendor }) => e('div', { className: 'vendor-page' }, 
    e('h2', null, vendorDashboard.strings.settings),
    e('p', null, 'Settings coming soon...')
);

// Loading Spinner Component
const LoadingSpinner = () => {
    return e('div', { className: 'vendor-loading' },
        e('div', { className: 'vendor-spinner' }),
        e('p', null, vendorDashboard.strings.loading)
    );
};

// Pagination Component
const Pagination = ({ currentPage, totalPages, onPageChange }) => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisible = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);

    if (endPage - startPage + 1 < maxVisible) {
        startPage = Math.max(1, endPage - maxVisible + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }

    return e('div', { className: 'vendor-pagination' },
        e('button', {
            className: 'vendor-pagination-btn',
            disabled: currentPage === 1,
            onClick: () => onPageChange(currentPage - 1)
        }, '‹'),

        pages.map(page =>
            e('button', {
                key: page,
                className: `vendor-pagination-btn ${page === currentPage ? 'active' : ''}`,
                onClick: () => onPageChange(page)
            }, page)
        ),

        e('button', {
            className: 'vendor-pagination-btn',
            disabled: currentPage === totalPages,
            onClick: () => onPageChange(currentPage + 1)
        }, '›')
    );
};

// Utility Functions
const formatCurrency = (amount) => {
    const { symbol, position, decimals, decimal_separator, thousand_separator } = vendorDashboard.currency;

    const formattedAmount = parseFloat(amount || 0).toFixed(decimals)
        .replace('.', decimal_separator)
        .replace(/\B(?=(\d{3})+(?!\d))/g, thousand_separator);

    switch (position) {
        case 'left':
            return symbol + formattedAmount;
        case 'right':
            return formattedAmount + symbol;
        case 'left_space':
            return symbol + ' ' + formattedAmount;
        case 'right_space':
            return formattedAmount + ' ' + symbol;
        default:
            return symbol + formattedAmount;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', () => {
    const dashboardRoot = document.getElementById('vendor-dashboard-root');
    if (dashboardRoot) {
        wp.element.render(e(VendorDashboard), dashboardRoot);
    }
});
