<?php
/**
 * Admin Vendors View
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_status = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Vendors', 'vendor'); ?></h1>
    <a href="<?php echo admin_url('user-new.php?role=vendor'); ?>" class="page-title-action"><?php _e('Add New Vendor', 'vendor'); ?></a>
    <hr class="wp-header-end">
    
    <div class="notice notice-info">
        <p>
            <strong><?php _e('How to add vendors:', 'vendor'); ?></strong><br>
            <?php _e('1. Click "Add New Vendor" to create a new user with vendor role', 'vendor'); ?><br>
            <?php _e('2. Or go to Users > All Users and change any existing user\'s role to "Vendor"', 'vendor'); ?><br>
            <?php _e('3. Vendors will be automatically added to this list and set to "Pending" status for your approval', 'vendor'); ?>
        </p>
    </div>
    
    <!-- Status Filter -->
    <ul class="subsubsub">
        <li class="all">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors'); ?>" <?php echo empty($current_status) ? 'class="current"' : ''; ?>>
                <?php _e('All', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count(); ?>)</span>
            </a> |
        </li>
        <li class="pending">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=pending'); ?>" <?php echo $current_status === 'pending' ? 'class="current"' : ''; ?>>
                <?php _e('Pending', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('pending'); ?>)</span>
            </a> |
        </li>
        <li class="active">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=active'); ?>" <?php echo $current_status === 'active' ? 'class="current"' : ''; ?>>
                <?php _e('Active', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('active'); ?>)</span>
            </a> |
        </li>
        <li class="rejected">
            <a href="<?php echo admin_url('admin.php?page=vendor-vendors&status=rejected'); ?>" <?php echo $current_status === 'rejected' ? 'class="current"' : ''; ?>>
                <?php _e('Rejected', 'vendor'); ?>
                <span class="count">(<?php echo vendor()->vendor_manager->get_vendor_count('rejected'); ?>)</span>
            </a>
        </li>
    </ul>
    
    <div class="tablenav top">
        <div class="alignleft actions bulkactions">
            <select name="action" id="bulk-action-selector-top">
                <option value="-1"><?php _e('Bulk Actions', 'vendor'); ?></option>
                <option value="approve"><?php _e('Approve', 'vendor'); ?></option>
                <option value="reject"><?php _e('Reject', 'vendor'); ?></option>
                <option value="delete"><?php _e('Delete', 'vendor'); ?></option>
            </select>
            <input type="submit" id="doaction" class="button action" value="<?php _e('Apply', 'vendor'); ?>">
        </div>
    </div>
    
    <table class="wp-list-table widefat fixed striped vendors">
        <thead>
            <tr>
                <td id="cb" class="manage-column column-cb check-column">
                    <input id="cb-select-all-1" type="checkbox">
                </td>
                <th scope="col" class="manage-column column-store-name column-primary">
                    <?php _e('Store Name', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-vendor-name">
                    <?php _e('Vendor Name', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-email">
                    <?php _e('Email', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-commission">
                    <?php _e('Commission Rate', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-status">
                    <?php _e('Status', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-date">
                    <?php _e('Date', 'vendor'); ?>
                </th>
                <th scope="col" class="manage-column column-actions">
                    <?php _e('Actions', 'vendor'); ?>
                </th>
            </tr>
        </thead>
        <tbody id="the-list">
            <?php if (!empty($vendors)): ?>
                <?php foreach ($vendors as $vendor): ?>
                    <?php 
                    $user = get_user_by('id', $vendor->user_id);
                    $status_class = 'status-' . $vendor->status;
                    ?>
                    <tr class="<?php echo esc_attr($status_class); ?>">
                        <th scope="row" class="check-column">
                            <input type="checkbox" name="vendor[]" value="<?php echo esc_attr($vendor->id); ?>">
                        </th>
                        <td class="store-name column-store-name column-primary">
                            <strong>
                                <a href="<?php echo admin_url('admin.php?page=vendor-vendors&action=view&vendor_id=' . $vendor->id); ?>">
                                    <?php echo esc_html($vendor->store_name); ?>
                                </a>
                            </strong>
                            <div class="row-actions">
                                <span class="view">
                                    <a href="<?php echo admin_url('admin.php?page=vendor-vendors&action=view&vendor_id=' . $vendor->id); ?>">
                                        <?php _e('View', 'vendor'); ?>
                                    </a> |
                                </span>
                                <span class="edit">
                                    <a href="<?php echo admin_url('user-edit.php?user_id=' . $vendor->user_id); ?>">
                                        <?php _e('Edit User', 'vendor'); ?>
                                    </a> |
                                </span>
                                <?php if ($vendor->status === 'pending'): ?>
                                    <span class="approve">
                                        <a href="#" class="vendor-approve" data-vendor-id="<?php echo esc_attr($vendor->id); ?>">
                                            <?php _e('Approve', 'vendor'); ?>
                                        </a> |
                                    </span>
                                    <span class="reject">
                                        <a href="#" class="vendor-reject" data-vendor-id="<?php echo esc_attr($vendor->id); ?>">
                                            <?php _e('Reject', 'vendor'); ?>
                                        </a> |
                                    </span>
                                <?php endif; ?>
                                <span class="delete">
                                    <a href="#" class="vendor-delete" data-vendor-id="<?php echo esc_attr($vendor->id); ?>">
                                        <?php _e('Delete', 'vendor'); ?>
                                    </a>
                                </span>
                            </div>
                        </td>
                        <td class="vendor-name column-vendor-name">
                            <?php echo $user ? esc_html($user->display_name) : __('Unknown', 'vendor'); ?>
                        </td>
                        <td class="email column-email">
                            <a href="mailto:<?php echo esc_attr($vendor->store_email); ?>">
                                <?php echo esc_html($vendor->store_email); ?>
                            </a>
                        </td>
                        <td class="commission column-commission">
                            <?php echo esc_html($vendor->commission_rate); ?>%
                        </td>
                        <td class="status column-status">
                            <span class="status-badge status-<?php echo esc_attr($vendor->status); ?>">
                                <?php echo esc_html(ucfirst($vendor->status)); ?>
                            </span>
                        </td>
                        <td class="date column-date">
                            <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($vendor->created_at))); ?>
                        </td>
                        <td class="actions column-actions">
                            <?php if ($vendor->status === 'pending'): ?>
                                <button type="button" class="button button-small vendor-approve" data-vendor-id="<?php echo esc_attr($vendor->id); ?>">
                                    <?php _e('Approve', 'vendor'); ?>
                                </button>
                                <button type="button" class="button button-small vendor-reject" data-vendor-id="<?php echo esc_attr($vendor->id); ?>">
                                    <?php _e('Reject', 'vendor'); ?>
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr class="no-items">
                    <td class="colspanchange" colspan="8">
                        <?php _e('No vendors found.', 'vendor'); ?>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<style>
.status-badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background-color: #ffb900;
    color: #fff;
}

.status-active {
    background-color: #46b450;
    color: #fff;
}

.status-rejected {
    background-color: #dc3232;
    color: #fff;
}

.vendor-approve, .vendor-reject {
    cursor: pointer;
}

.notice.notice-info {
    margin-top: 10px;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.vendor-approve').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_approve)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $button = $(this);
        
        $button.prop('disabled', true).text(vendorAdmin.strings.processing);
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_approve_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                    $button.prop('disabled', false).text('<?php _e('Approve', 'vendor'); ?>');
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
                $button.prop('disabled', false).text('<?php _e('Approve', 'vendor'); ?>');
            }
        });
    });
    
    $('.vendor-reject').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm(vendorAdmin.strings.confirm_reject)) {
            return;
        }
        
        var vendorId = $(this).data('vendor-id');
        var $button = $(this);
        
        $button.prop('disabled', true).text(vendorAdmin.strings.processing);
        
        $.ajax({
            url: vendorAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'vendor_reject_vendor',
                vendor_id: vendorId,
                nonce: vendorAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || vendorAdmin.strings.error);
                    $button.prop('disabled', false).text('<?php _e('Reject', 'vendor'); ?>');
                }
            },
            error: function() {
                alert(vendorAdmin.strings.error);
                $button.prop('disabled', false).text('<?php _e('Reject', 'vendor'); ?>');
            }
        });
    });
});
</script>