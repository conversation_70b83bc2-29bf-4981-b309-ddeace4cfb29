<?php
/**
 * Vendor Frontend
 *
 * @package Vendor
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Frontend class
 */
class Vendor_Frontend {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('vendor_dashboard', array($this, 'dashboard_shortcode'));
        add_action('template_redirect', array($this, 'dashboard_template_redirect'));
        add_action('wp', array($this, 'hide_admin_bar_on_dashboard'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Add rewrite rules for vendor dashboard
        add_rewrite_rule('^vendor-dashboard/?$', 'index.php?vendor_dashboard=1', 'top');
        add_rewrite_rule('^vendor-dashboard/([^/]+)/?$', 'index.php?vendor_dashboard=1&vendor_page=$matches[1]', 'top');
        
        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
    }
    
    /**
     * Add query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'vendor_dashboard';
        $vars[] = 'vendor_page';
        return $vars;
    }
    
    /**
     * Enqueue scripts
     */
    public function enqueue_scripts() {
        if ($this->is_vendor_dashboard()) {
            // Enqueue React and dependencies
            wp_enqueue_script('vendor-react', VENDOR_PLUGIN_URL . 'assets/js/vendor-dashboard.js', array('wp-element', 'wp-api-fetch'), VENDOR_VERSION, true);
            wp_enqueue_style('vendor-dashboard', VENDOR_PLUGIN_URL . 'assets/css/vendor-dashboard.css', array(), VENDOR_VERSION);
            
            // Localize script
            wp_localize_script('vendor-react', 'vendorDashboard', array(
                'apiUrl' => rest_url('vendor/v1/'),
                'nonce' => wp_create_nonce('wp_rest'),
                'currentUser' => wp_get_current_user(),
                'strings' => array(
                    'dashboard' => __('Dashboard', 'vendor'),
                    'products' => __('Products', 'vendor'),
                    'orders' => __('Orders', 'vendor'),
                    'coupons' => __('Coupons', 'vendor'),
                    'analytics' => __('Analytics', 'vendor'),
                    'reviews' => __('Reviews', 'vendor'),
                    'withdrawals' => __('Withdrawals', 'vendor'),
                    'courses' => __('My Courses', 'vendor'),
                    'announcements' => __('Manage Announcements', 'vendor'),
                    'qa' => __('Q&A', 'vendor'),
                    'quiz_attempts' => __('Quiz Attempts', 'vendor'),
                    'students' => __('Students', 'vendor'),
                    'settings' => __('Settings', 'vendor'),
                    'store' => __('Go to Store', 'vendor'),
                    'store_settings' => __('Store Settings', 'vendor'),
                    'logout' => __('Logout', 'vendor'),
                    'loading' => __('Loading...', 'vendor'),
                    'error' => __('An error occurred', 'vendor'),
                    'success' => __('Success!', 'vendor'),
                    'save' => __('Save', 'vendor'),
                    'cancel' => __('Cancel', 'vendor'),
                    'edit' => __('Edit', 'vendor'),
                    'delete' => __('Delete', 'vendor'),
                    'view' => __('View', 'vendor'),
                    'add_new' => __('Add New', 'vendor'),
                    'search' => __('Search', 'vendor'),
                    'filter' => __('Filter', 'vendor'),
                    'all' => __('All', 'vendor'),
                    'active' => __('Active', 'vendor'),
                    'pending' => __('Pending', 'vendor'),
                    'completed' => __('Completed', 'vendor'),
                    'cancelled' => __('Cancelled', 'vendor')
                ),
                'currency' => array(
                    'symbol' => get_woocommerce_currency_symbol(),
                    'position' => get_option('woocommerce_currency_pos'),
                    'decimal_separator' => wc_get_price_decimal_separator(),
                    'thousand_separator' => wc_get_price_thousand_separator(),
                    'decimals' => wc_get_price_decimals()
                ),
                'dateFormat' => get_option('date_format'),
                'timeFormat' => get_option('time_format'),
                'tutorActive' => class_exists('TUTOR\Tutor')
            ));
        }
    }
    
    /**
     * Check if current page is vendor dashboard
     */
    private function is_vendor_dashboard() {
        global $wp_query;
        
        return isset($wp_query->query_vars['vendor_dashboard']) || 
               is_page(get_option('vendor_dashboard_page_id')) ||
               (isset($_GET['page']) && $_GET['page'] === 'vendor-dashboard');
    }
    
    /**
     * Dashboard shortcode
     */
    public function dashboard_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to access vendor dashboard.', 'vendor') . '</p>';
        }
        
        if (!vendor()->vendor_manager->is_vendor()) {
            return '<p>' . __('You do not have permission to access vendor dashboard.', 'vendor') . '</p>';
        }
        
        // Hide admin bar when shortcode is used
        add_filter('show_admin_bar', '__return_false');
        
        // Add full screen styles for shortcode
        add_action('wp_footer', array($this, 'shortcode_footer_styles'));
        
        ob_start();
        $this->render_dashboard();
        return ob_get_clean();
    }
    
    /**
     * Dashboard template redirect
     */
    public function dashboard_template_redirect() {
        global $wp_query;
        
        if (isset($wp_query->query_vars['vendor_dashboard']) || is_page(get_option('vendor_dashboard_page_id'))) {
            if (!is_user_logged_in()) {
                wp_redirect(wp_login_url(get_permalink()));
                exit;
            }
            
            if (!vendor()->vendor_manager->is_vendor()) {
                wp_die(__('You do not have permission to access this page.', 'vendor'));
            }
            
            // Hide admin bar for vendor dashboard
            add_filter('show_admin_bar', '__return_false');
            
            $this->load_dashboard_template();
        }
    }
    
    /**
     * Load dashboard template
     */
    private function load_dashboard_template() {
        // Try to load custom template from theme
        $template = locate_template('vendor/dashboard.php');
        
        if (!$template) {
            $template = VENDOR_PLUGIN_DIR . 'templates/dashboard.php';
        }
        
        include $template;
        exit;
    }
    
    /**
     * Render dashboard
     */
    private function render_dashboard() {
        ?>
        <div id="vendor-dashboard-root" class="vendor-dashboard-wrapper">
            <div class="vendor-dashboard-loading">
                <div class="vendor-spinner"></div>
                <p><?php _e('Loading dashboard...', 'vendor'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Get vendor dashboard URL
     */
    public static function get_dashboard_url($page = '') {
        $dashboard_page_id = get_option('vendor_dashboard_page_id');
        
        if ($dashboard_page_id) {
            $url = get_permalink($dashboard_page_id);
        } else {
            $url = home_url('/vendor-dashboard/');
        }
        
        if (!empty($page)) {
            $url = trailingslashit($url) . $page . '/';
        }
        
        return $url;
    }
    
    /**
     * Get vendor store URL
     */
    public static function get_store_url($vendor_id) {
        $vendor = vendor()->vendor_manager->get_vendor($vendor_id);
        
        if ($vendor && $vendor->store_slug) {
            return home_url('/store/' . $vendor->store_slug . '/');
        }
        
        return home_url();
    }
    
    /**
     * Hide admin bar on vendor dashboard
     */
    public function hide_admin_bar_on_dashboard() {
        if ($this->is_vendor_dashboard()) {
            add_filter('show_admin_bar', '__return_false');
            
            // Remove admin bar styles and scripts
            remove_action('wp_head', '_admin_bar_bump_cb');
            
            // Add custom CSS to ensure full screen
            add_action('wp_head', array($this, 'dashboard_head_styles'), 999);
        }
    }
    
    /**
     * Add dashboard head styles
     */
    public function dashboard_head_styles() {
        ?>
        <style type="text/css">
            /* Force hide admin bar and reset margins */
            html { margin-top: 0 !important; }
            * html body { margin-top: 0 !important; }
            @media screen and ( max-width: 782px ) {
                html { margin-top: 0 !important; }
                * html body { margin-top: 0 !important; }
            }
            
            /* Hide only theme wrappers, preserve dashboard content */
            body.vendor-dashboard-page > *:not(#vendor-dashboard-root):not(script):not(style) {
                display: none !important;
            }
            
            /* Ensure dashboard elements are visible */
            #vendor-dashboard-root * {
                position: relative !important;
            }
            
            #vendor-dashboard-root .vendor-sidebar,
            #vendor-dashboard-root .vendor-header,
            #vendor-dashboard-root .vendor-nav,
            #vendor-dashboard-root header,
            #vendor-dashboard-root nav,
            #vendor-dashboard-root .sidebar {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            /* Ensure dashboard wrapper is always on top */
            #vendor-dashboard-root {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                z-index: 999999 !important;
                background: #f1f1f1 !important;
            }
        </style>
        <?php
    }
    
    /**
     * Add footer styles for shortcode
     */
    public function shortcode_footer_styles() {
        ?>
        <style type="text/css">
            /* Full screen styles for shortcode */
            .vendor-dashboard-wrapper {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                z-index: 999999 !important;
                background: #f1f1f1 !important;
            }
            
            /* Hide only theme elements when shortcode is active */
            body:has(.vendor-dashboard-wrapper) > header:not(.vendor-header),
            body:has(.vendor-dashboard-wrapper) > .site-header:not(.vendor-header),
            body:has(.vendor-dashboard-wrapper) > #header:not(.vendor-header),
            body:has(.vendor-dashboard-wrapper) > .header:not(.vendor-header),
            body:has(.vendor-dashboard-wrapper) > nav:not(.vendor-nav),
            body:has(.vendor-dashboard-wrapper) > .navigation:not(.vendor-nav),
            body:has(.vendor-dashboard-wrapper) > .navbar:not(.vendor-nav),
            body:has(.vendor-dashboard-wrapper) > .menu:not(.vendor-menu),
            body:has(.vendor-dashboard-wrapper) > footer:not(.vendor-footer),
            body:has(.vendor-dashboard-wrapper) > .site-footer:not(.vendor-footer),
            body:has(.vendor-dashboard-wrapper) > #footer:not(.vendor-footer),
            body:has(.vendor-dashboard-wrapper) > .footer:not(.vendor-footer),
            body:has(.vendor-dashboard-wrapper) > .sidebar:not(.vendor-sidebar),
            body:has(.vendor-dashboard-wrapper) > #sidebar:not(.vendor-sidebar),
            body:has(.vendor-dashboard-wrapper) > .widget-area:not(.vendor-widget),
            body:has(.vendor-dashboard-wrapper) > .breadcrumb:not(.vendor-breadcrumb),
            body:has(.vendor-dashboard-wrapper) > .breadcrumbs:not(.vendor-breadcrumb),
            body:has(.vendor-dashboard-wrapper) #wpadminbar {
                display: none !important;
            }
            
            /* Preserve dashboard elements */
            .vendor-dashboard-wrapper .vendor-sidebar,
            .vendor-dashboard-wrapper .vendor-header,
            .vendor-dashboard-wrapper .vendor-nav,
            .vendor-dashboard-wrapper header,
            .vendor-dashboard-wrapper nav,
            .vendor-dashboard-wrapper .sidebar {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            /* Reset body margins */
            body:has(.vendor-dashboard-wrapper) {
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
            }
            
            html:has(.vendor-dashboard-wrapper) {
                margin-top: 0 !important;
            }
        </style>
        
        <script>
            // JavaScript fallback for browsers that don't support :has()
            document.addEventListener('DOMContentLoaded', function() {
                const dashboardWrapper = document.querySelector('.vendor-dashboard-wrapper');
                if (dashboardWrapper) {
                    // Hide only theme elements that are direct children of body
                    const themeSelectors = [
                        'body > header:not(.vendor-header)',
                        'body > .site-header:not(.vendor-header)',
                        'body > #header:not(.vendor-header)',
                        'body > .header:not(.vendor-header)',
                        'body > nav:not(.vendor-nav)',
                        'body > .navigation:not(.vendor-nav)',
                        'body > .navbar:not(.vendor-nav)',
                        'body > .menu:not(.vendor-menu)',
                        'body > footer:not(.vendor-footer)',
                        'body > .site-footer:not(.vendor-footer)',
                        'body > #footer:not(.vendor-footer)',
                        'body > .footer:not(.vendor-footer)',
                        'body > .sidebar:not(.vendor-sidebar)',
                        'body > #sidebar:not(.vendor-sidebar)',
                        'body > .widget-area:not(.vendor-widget)',
                        'body > .breadcrumb:not(.vendor-breadcrumb)',
                        'body > .breadcrumbs:not(.vendor-breadcrumb)',
                        '#wpadminbar'
                    ];
                    
                    themeSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.display = 'none';
                        });
                    });
                    
                    // Ensure dashboard elements remain visible
                    const dashboardElements = dashboardWrapper.querySelectorAll('header, nav, .sidebar, .vendor-sidebar, .vendor-header, .vendor-nav');
                    dashboardElements.forEach(el => {
                        el.style.display = '';
                        el.style.visibility = 'visible';
                        el.style.opacity = '1';
                    });
                    
                    // Set body styles
                    document.body.style.margin = '0';
                    document.body.style.padding = '0';
                    document.body.style.overflow = 'hidden';
                    document.documentElement.style.marginTop = '0';
                    
                    // Hide direct body children except dashboard and essential elements
                    Array.from(document.body.children).forEach(child => {
                        if (child.id !== 'vendor-dashboard-root' && 
                            child.tagName !== 'SCRIPT' && 
                            child.tagName !== 'STYLE' &&
                            !child.classList.contains('vendor-dashboard-wrapper')) {
                            child.style.display = 'none';
                        }
                    });
                }
            });
        </script>
        <?php
    }
}
