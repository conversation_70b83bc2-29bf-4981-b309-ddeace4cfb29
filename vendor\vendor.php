<?php
/**
 * Plugin Name: Vendor - Multi-vendor Marketplace
 * Plugin URI: https://example.com/vendor
 * Description: Modern React-based multi-vendor marketplace plugin with advanced vendor management, commission system, withdrawal management, and Tutor LMS integration.
 * Version: 1.0.0
 * Author: Vendor Team
 * Author URI: https://example.com
 * Text Domain: vendor
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VENDOR_VERSION', '1.0.0');
define('VENDOR_PLUGIN_FILE', __FILE__);
define('VENDOR_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('VENDOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('VENDOR_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('VENDOR_TEXT_DOMAIN', 'vendor');

// Check if WooCommerce is active
function vendor_check_woocommerce() {
    // Check if WooCommerce plugin is installed and active
    if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
        add_action('admin_notices', 'vendor_woocommerce_missing_notice');
        return false;
    }
    
    // Also check if WooCommerce class exists (for late loading)
    if (!class_exists('WooCommerce')) {
        return false;
    }
    
    return true;
}

/**
 * WooCommerce missing notice
 */
function vendor_woocommerce_missing_notice() {
    ?>
    <div class="notice notice-error">
        <p><?php _e('Vendor plugin requires WooCommerce to be installed and active.', 'vendor'); ?></p>
    </div>
    <?php
}

// Autoloader
$autoloader_file = VENDOR_PLUGIN_DIR . 'includes/class-vendor-autoloader.php';
if (file_exists($autoloader_file)) {
    require_once $autoloader_file;
    Vendor_Autoloader::init();
} else {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>Vendor plugin: Autoloader file not found.</p></div>';
    });
    return;
}

// Main plugin class
final class Vendor {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = VENDOR_VERSION;
    
    /**
     * Database manager
     */
    public $db;
    
    /**
     * Admin manager
     */
    public $admin;
    
    /**
     * Frontend manager
     */
    public $frontend;
    
    /**
     * API manager
     */
    public $api;
    
    /**
     * Vendor manager
     */
    public $vendor_manager;
    
    /**
     * Commission manager
     */
    public $commission;
    
    /**
     * Withdrawal manager
     */
    public $withdrawal;

    /**
     * Gateway manager
     */
    public $gateway_manager;

    /**
     * Tutor integration
     */
    public $tutor_integration;
    
    /**
     * Get plugin instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_components();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Activation/Deactivation hooks
        register_activation_hook(VENDOR_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(VENDOR_PLUGIN_FILE, array($this, 'deactivate'));
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Initialize components after plugins are loaded
        add_action('plugins_loaded', array($this, 'init_after_plugins_loaded'), 20);
    }
    
    /**
     * Initialize components after plugins are loaded
     */
    public function init_after_plugins_loaded() {
        // Check WooCommerce dependency
        if (!vendor_check_woocommerce()) {
            return;
        }
        
        try {
            // Database manager
            if (class_exists('Vendor_Database')) {
                $this->db = new Vendor_Database();
            }
            
            // Admin manager
            if (is_admin() && class_exists('Vendor_Admin')) {
                $this->admin = new Vendor_Admin();
            }
            
            // Frontend manager
            if ((!is_admin() || wp_doing_ajax()) && class_exists('Vendor_Frontend')) {
                $this->frontend = new Vendor_Frontend();
            }
            
            // API manager
            if (class_exists('Vendor_API')) {
                $this->api = new Vendor_API();
            }
            
            // Core managers
            if (class_exists('Vendor_Manager')) {
                $this->vendor_manager = new Vendor_Manager();
            }
            
            if (class_exists('Vendor_Commission')) {
                $this->commission = new Vendor_Commission();
            }
            
            if (class_exists('Vendor_Withdrawal')) {
                $this->withdrawal = new Vendor_Withdrawal();
            }
            
            if (class_exists('Vendor_Gateway_Manager')) {
                $this->gateway_manager = new Vendor_Gateway_Manager();
            }

            // Integrations
            if (class_exists('Vendor_Tutor_Integration')) {
                $this->tutor_integration = new Vendor_Tutor_Integration();
            }
            
        } catch (Exception $e) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>Vendor Plugin Error: ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize components that need WordPress to be loaded
        do_action('vendor_init');
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('vendor', false, dirname(VENDOR_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if WooCommerce plugin is installed and active
        if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
            wp_die(__('Vendor plugin requires WooCommerce to be installed and active.', 'vendor'));
            return;
        }
        
        // Initialize database manager for activation
        if (class_exists('Vendor_Database')) {
            $db = new Vendor_Database();
            $db->create_tables();
        }
        
        // Create vendor role
        $this->create_vendor_role();
        
        // Create vendor dashboard page
        $this->create_vendor_page();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('vendor_activated', true);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Remove activation flag
        delete_option('vendor_activated');
    }
    
    /**
     * Create vendor role
     */
    private function create_vendor_role() {
        add_role('vendor', __('Vendor', 'vendor'), array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'publish_posts' => false,
            'upload_files' => true,
            'edit_products' => true,
            'read_products' => true,
            'delete_products' => true,
            'edit_published_products' => true,
            'delete_published_products' => true,
            'edit_shop_orders' => true,
            'read_shop_orders' => true,
            'edit_shop_coupons' => true,
            'read_shop_coupons' => true,
            'delete_shop_coupons' => true,
            'edit_published_shop_coupons' => true,
            'delete_published_shop_coupons' => true,
        ));
    }
    
    /**
     * Create vendor dashboard page
     */
    private function create_vendor_page() {
        $page_id = wp_insert_post(array(
            'post_title' => __('Vendor Dashboard', 'vendor'),
            'post_content' => '[vendor_dashboard]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_slug' => 'vendor-dashboard'
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_option('vendor_dashboard_page_id', $page_id);
        }
    }
    
    /**
     * Get plugin URL
     */
    public function plugin_url() {
        return untrailingslashit(plugins_url('/', VENDOR_PLUGIN_FILE));
    }
    
    /**
     * Get plugin path
     */
    public function plugin_path() {
        return untrailingslashit(plugin_dir_path(VENDOR_PLUGIN_FILE));
    }
    
    /**
     * Get template path
     */
    public function template_path() {
        return apply_filters('vendor_template_path', 'vendor/');
    }
}

/**
 * Get main plugin instance
 */
function vendor() {
    return Vendor::instance();
}

// Initialize plugin
vendor();
