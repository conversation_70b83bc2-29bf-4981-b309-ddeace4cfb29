<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php _e('Vendor Dashboard', 'vendor'); ?> - <?php bloginfo('name'); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Hide theme elements */
        body.vendor-dashboard-page {
            margin: 0 !important;
            padding: 0 !important;
            background: #f1f1f1 !important;
            overflow-x: hidden;
        }
        
        /* Hide only theme elements, not dashboard elements */
        body.vendor-dashboard-page > header:not(.vendor-header),
        body.vendor-dashboard-page > .site-header:not(.vendor-header),
        body.vendor-dashboard-page > #header:not(.vendor-header),
        body.vendor-dashboard-page > .header:not(.vendor-header),
        body.vendor-dashboard-page > nav:not(.vendor-nav),
        body.vendor-dashboard-page > .navigation:not(.vendor-nav),
        body.vendor-dashboard-page > .navbar:not(.vendor-nav),
        body.vendor-dashboard-page > .menu:not(.vendor-menu),
        body.vendor-dashboard-page > footer:not(.vendor-footer),
        body.vendor-dashboard-page > .site-footer:not(.vendor-footer),
        body.vendor-dashboard-page > #footer:not(.vendor-footer),
        body.vendor-dashboard-page > .footer:not(.vendor-footer),
        body.vendor-dashboard-page > .sidebar:not(.vendor-sidebar),
        body.vendor-dashboard-page > #sidebar:not(.vendor-sidebar),
        body.vendor-dashboard-page > .widget-area:not(.vendor-widget),
        body.vendor-dashboard-page > .breadcrumb:not(.vendor-breadcrumb),
        body.vendor-dashboard-page > .breadcrumbs:not(.vendor-breadcrumb),
        body.vendor-dashboard-page #wpadminbar {
            display: none !important;
        }
        
        /* Hide theme containers but preserve dashboard content */
        body.vendor-dashboard-page > .container:not(.vendor-container),
        body.vendor-dashboard-page > .wrapper:not(.vendor-wrapper),
        body.vendor-dashboard-page > .main:not(.vendor-main),
        body.vendor-dashboard-page > #main:not(.vendor-main),
        body.vendor-dashboard-page > .content:not(.vendor-content),
        body.vendor-dashboard-page > #content:not(.vendor-content),
        body.vendor-dashboard-page > .site:not(.vendor-site),
        body.vendor-dashboard-page > #site:not(.vendor-site) {
            display: none !important;
        }
        
        /* Full screen dashboard */
        .vendor-dashboard-wrapper {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: #f1f1f1;
            z-index: 999999;
            overflow: hidden;
        }
        
        /* Loading styles */
        .vendor-dashboard-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: #f1f1f1;
        }
        
        .vendor-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e3e3e3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: vendor-spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes vendor-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .vendor-dashboard-loading p {
            margin: 0;
            color: #666;
            font-size: 16px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        /* Hide WordPress admin bar even if logged in */
        html {
            margin-top: 0 !important;
        }
        
        /* Preserve dashboard elements */
        .vendor-dashboard-wrapper * {
            position: relative !important;
        }
        
        /* Ensure dashboard sidebar and header are visible */
        .vendor-dashboard-wrapper .vendor-sidebar,
        .vendor-dashboard-wrapper .vendor-header,
        .vendor-dashboard-wrapper .vendor-nav,
        .vendor-dashboard-wrapper .vendor-menu,
        .vendor-dashboard-wrapper header,
        .vendor-dashboard-wrapper nav,
        .vendor-dashboard-wrapper .sidebar {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .vendor-dashboard-wrapper {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }
        }
    </style>
</head>
<body <?php body_class('vendor-dashboard-page'); ?>>
    
    <div id="vendor-dashboard-root" class="vendor-dashboard-wrapper">
        <div class="vendor-dashboard-loading">
            <div class="vendor-spinner"></div>
            <p><?php _e('Loading dashboard...', 'vendor'); ?></p>
        </div>
    </div>
    
    <?php wp_footer(); ?>
    
    <script>
        // Additional JavaScript to ensure full screen
        document.addEventListener('DOMContentLoaded', function() {
            // Hide only theme elements that are direct children of body
            const themeSelectors = [
                'body > header:not(.vendor-header)',
                'body > .site-header:not(.vendor-header)',
                'body > #header:not(.vendor-header)',
                'body > .header:not(.vendor-header)',
                'body > nav:not(.vendor-nav)',
                'body > .navigation:not(.vendor-nav)',
                'body > .navbar:not(.vendor-nav)',
                'body > .menu:not(.vendor-menu)',
                'body > footer:not(.vendor-footer)',
                'body > .site-footer:not(.vendor-footer)',
                'body > #footer:not(.vendor-footer)',
                'body > .footer:not(.vendor-footer)',
                'body > .sidebar:not(.vendor-sidebar)',
                'body > #sidebar:not(.vendor-sidebar)',
                'body > .widget-area:not(.vendor-widget)',
                'body > .breadcrumb:not(.vendor-breadcrumb)',
                'body > .breadcrumbs:not(.vendor-breadcrumb)',
                '#wpadminbar'
            ];
            
            themeSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                });
            });
            
            // Ensure dashboard elements remain visible
            const dashboardWrapper = document.querySelector('.vendor-dashboard-wrapper');
            if (dashboardWrapper) {
                const dashboardElements = dashboardWrapper.querySelectorAll('header, nav, .sidebar, .vendor-sidebar, .vendor-header, .vendor-nav');
                dashboardElements.forEach(el => {
                    el.style.display = '';
                    el.style.visibility = 'visible';
                    el.style.opacity = '1';
                });
            }
            
            // Ensure body styles
            document.body.style.margin = '0';
            document.body.style.padding = '0';
            document.body.style.overflow = 'hidden';
            
            // Remove admin bar margin
            document.documentElement.style.marginTop = '0';
        });
    </script>
</body>
</html>
